/* Execution Overlay Styles */

.execution-overlay {
    position: fixed;
    top: 0;
    right: 0;
    width: 350px;
    max-height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    z-index: 9999;
    border-radius: 0 0 0 10px;
    box-shadow: -2px 2px 10px rgba(0, 0, 0, 0.3);
    overflow-y: auto;
    display: none; /* Hidden by default */
    backdrop-filter: blur(2px);
    transition: all 0.3s ease;
}

.execution-overlay-header {
    padding: 10px 15px;
    background-color: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 1;
}

.execution-overlay-title {
    font-size: 16px;
    font-weight: bold;
    margin: 0;
}

.execution-overlay-close {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
}

.execution-overlay-close:hover {
    color: white;
}

.execution-overlay-content {
    padding: 10px;
}

/* Action item styles in the overlay */
.overlay-action-item {
    padding: 8px 10px;
    margin-bottom: 8px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
}

.overlay-action-item .badge {
    margin-right: 8px;
}

/* Status styles */
.overlay-action-item.executing {
    background-color: rgba(13, 110, 253, 0.3);
    border-left: 4px solid #0d6efd;
    animation: pulse-blue-overlay 2s infinite;
}

.overlay-action-item.success {
    background-color: rgba(25, 135, 84, 0.3);
    border-left: 4px solid #198754;
}

.overlay-action-item.error {
    background-color: rgba(220, 53, 69, 0.3);
    border-left: 4px solid #dc3545;
}

@keyframes pulse-blue-overlay {
    0% { background-color: rgba(13, 110, 253, 0.3); }
    50% { background-color: rgba(13, 110, 253, 0.5); }
    100% { background-color: rgba(13, 110, 253, 0.3); }
}

/* Status icons */
.overlay-action-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    margin-right: 8px;
}

.overlay-action-item.success .overlay-action-status i {
    color: #28a745;
}

.overlay-action-item.error .overlay-action-status i {
    color: #dc3545;
}

/* Make the overlay draggable */
.execution-overlay-header {
    cursor: move;
}

/* Resize handle */
.execution-overlay-resize {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 15px;
    height: 15px;
    cursor: nwse-resize;
    background: linear-gradient(135deg, transparent 50%, rgba(255, 255, 255, 0.5) 50%);
    border-radius: 0 0 0 5px;
}
