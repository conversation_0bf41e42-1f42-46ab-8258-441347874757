/* Validation result styles */
.validation-result {
    min-height: 30px;
    transition: all 0.3s ease;
}

.validation-result .alert {
    font-size: 0.9rem;
    margin-bottom: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.validation-result .alert i {
    margin-right: 5px;
}

/* Validation states */
.validating {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.validation-complete {
    opacity: 1;
}

/* Highlight animation for validation results */
.highlight-result {
    animation: pulse 1.5s ease;
}

@keyframes pulse {
    0% { transform: scale(1); }
    10% { transform: scale(1.03); }
    20% { transform: scale(1); }
    30% { transform: scale(1.02); }
    40% { transform: scale(1); }
    100% { transform: scale(1); }
}

/* Fade-in animation for alerts */
.fade-in {
    animation: fadeIn 0.4s ease-in;
}

@keyframes fadeIn {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

/* Add a border to validation success to make it more noticeable */
.validation-result .alert-success {
    border-left: 4px solid #198754;
}

.validation-result .alert-danger {
    border-left: 4px solid #dc3545;
}

.validation-result .alert-warning {
    border-left: 4px solid #ffc107;
} 