/**
 * Execution Overlay Manager
 * Handles the transparent overlay that shows the action list during execution
 */
class ExecutionOverlayManager {
    constructor(appInstance) {
        this.app = appInstance;
        this.overlay = null;
        this.overlayContent = null;
        this.actionItems = new Map(); // Map to store action items by index
        this.isVisible = false;
        this.isDragging = false;
        this.isResizing = false;
        this.dragStartX = 0;
        this.dragStartY = 0;
        this.initialWidth = 350;
        this.initialHeight = 0;
        
        // Create the overlay element
        this.createOverlay();
        
        // Add event listeners for dragging and resizing
        this.setupDragAndResize();
    }
    
    /**
     * Create the overlay DOM element
     */
    createOverlay() {
        // Create the overlay container
        this.overlay = document.createElement('div');
        this.overlay.className = 'execution-overlay';
        
        // Create the header
        const header = document.createElement('div');
        header.className = 'execution-overlay-header';
        header.innerHTML = `
            <h5 class="execution-overlay-title">Executing Actions</h5>
            <button class="execution-overlay-close" title="Close Overlay">×</button>
        `;
        
        // Create the content container
        this.overlayContent = document.createElement('div');
        this.overlayContent.className = 'execution-overlay-content';
        
        // Create resize handle
        const resizeHandle = document.createElement('div');
        resizeHandle.className = 'execution-overlay-resize';
        
        // Add elements to the overlay
        this.overlay.appendChild(header);
        this.overlay.appendChild(this.overlayContent);
        this.overlay.appendChild(resizeHandle);
        
        // Add the overlay to the document body
        document.body.appendChild(this.overlay);
        
        // Add event listener for close button
        const closeButton = this.overlay.querySelector('.execution-overlay-close');
        closeButton.addEventListener('click', () => this.hide());
    }
    
    /**
     * Setup drag and resize functionality
     */
    setupDragAndResize() {
        const header = this.overlay.querySelector('.execution-overlay-header');
        const resizeHandle = this.overlay.querySelector('.execution-overlay-resize');
        
        // Drag functionality
        header.addEventListener('mousedown', (e) => {
            if (e.target.classList.contains('execution-overlay-close')) return;
            
            this.isDragging = true;
            this.dragStartX = e.clientX;
            this.dragStartY = e.clientY;
            
            const rect = this.overlay.getBoundingClientRect();
            this.initialLeft = rect.left;
            this.initialTop = rect.top;
            
            document.addEventListener('mousemove', this.handleDragMove);
            document.addEventListener('mouseup', this.handleDragEnd);
        });
        
        // Resize functionality
        resizeHandle.addEventListener('mousedown', (e) => {
            this.isResizing = true;
            this.dragStartX = e.clientX;
            this.dragStartY = e.clientY;
            
            const rect = this.overlay.getBoundingClientRect();
            this.initialWidth = rect.width;
            this.initialHeight = rect.height;
            
            document.addEventListener('mousemove', this.handleResizeMove);
            document.addEventListener('mouseup', this.handleResizeEnd);
        });
    }
    
    /**
     * Handle drag movement
     */
    handleDragMove = (e) => {
        if (!this.isDragging) return;
        
        const deltaX = e.clientX - this.dragStartX;
        const deltaY = e.clientY - this.dragStartY;
        
        this.overlay.style.left = `${this.initialLeft + deltaX}px`;
        this.overlay.style.top = `${this.initialTop + deltaY}px`;
        this.overlay.style.right = 'auto';
    };
    
    /**
     * Handle drag end
     */
    handleDragEnd = () => {
        this.isDragging = false;
        document.removeEventListener('mousemove', this.handleDragMove);
        document.removeEventListener('mouseup', this.handleDragEnd);
    };
    
    /**
     * Handle resize movement
     */
    handleResizeMove = (e) => {
        if (!this.isResizing) return;
        
        const deltaX = this.dragStartX - e.clientX;
        const deltaY = e.clientY - this.dragStartY;
        
        const newWidth = this.initialWidth + deltaX;
        const newHeight = this.initialHeight + deltaY;
        
        if (newWidth >= 250) {
            this.overlay.style.width = `${newWidth}px`;
        }
        
        if (newHeight >= 200) {
            this.overlay.style.height = `${newHeight}px`;
        }
    };
    
    /**
     * Handle resize end
     */
    handleResizeEnd = () => {
        this.isResizing = false;
        document.removeEventListener('mousemove', this.handleResizeMove);
        document.removeEventListener('mouseup', this.handleResizeEnd);
    };
    
    /**
     * Show the overlay
     */
    show() {
        this.overlay.style.display = 'block';
        this.isVisible = true;
        
        // Reset position to default (top right)
        this.overlay.style.right = '0';
        this.overlay.style.left = 'auto';
        this.overlay.style.top = '0';
        
        // Clear any previous content
        this.overlayContent.innerHTML = '';
        this.actionItems.clear();
    }
    
    /**
     * Hide the overlay
     */
    hide() {
        this.overlay.style.display = 'none';
        this.isVisible = false;
    }

    /**
     * Toggle the overlay visibility
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    /**
     * Create action items in the overlay based on the current actions list
     * @param {Array} actions - The list of actions to display
     */
    createActionItems(actions) {
        if (!actions || !Array.isArray(actions)) return;
        
        // Clear previous content
        this.overlayContent.innerHTML = '';
        this.actionItems.clear();
        
        // Create action items
        actions.forEach((action, index) => {
            const actionItem = document.createElement('div');
            actionItem.className = 'overlay-action-item';
            actionItem.dataset.actionIndex = index;
            
            // Get action description
            const actionText = this.app.actionManager.getActionDescription(action);
            
            // Create action content
            actionItem.innerHTML = `
                <span class="badge bg-secondary me-2">${index + 1}</span>
                <span class="badge bg-primary me-2">${action.type}</span>
                <div class="overlay-action-status"></div>
                <span class="overlay-action-text">${actionText}</span>
            `;
            
            // Add to content and store in map
            this.overlayContent.appendChild(actionItem);
            this.actionItems.set(index, actionItem);
        });
    }
    
    /**
     * Update the status of an action item in the overlay
     * @param {number} index - The index of the action
     * @param {string} status - The status ('executing', 'success', 'error')
     * @param {string} message - Optional message
     */
    updateActionStatus(index, status, message = '') {
        const actionItem = this.actionItems.get(index);
        if (!actionItem) return;
        
        // Remove previous status classes
        actionItem.classList.remove('executing', 'success', 'error');
        
        // Add appropriate status class
        actionItem.classList.add(status);
        
        // Update status icon
        const statusElement = actionItem.querySelector('.overlay-action-status');
        if (statusElement) {
            switch (status) {
                case 'executing':
                    statusElement.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span>';
                    // Scroll to this item
                    actionItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    break;
                case 'success':
                    statusElement.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
                    break;
                case 'error':
                    statusElement.innerHTML = '<i class="bi bi-x-circle-fill text-danger"></i>';
                    break;
                default:
                    statusElement.innerHTML = '';
            }
        }
        
        // Add tooltip if message is provided
        if (message) {
            actionItem.setAttribute('title', message);
        } else {
            actionItem.removeAttribute('title');
        }
    }
}

// Export the class
window.ExecutionOverlayManager = ExecutionOverlayManager;
