from .base_action import BaseAction
import os
import logging

class DoubleClickImageAction(BaseAction):
    """Handler for double clicking on images using AirTest"""

    def execute(self, params):
        """
        Execute double click on image action

        Args:
            params: Dictionary containing:
                - image_path: Path to the target image
                - threshold: (Optional) Similarity threshold (0.0-1.0, default: 0.7)
                - timeout: (Optional) Timeout in seconds (default: 20)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        image_path = params.get('image_path')
        threshold = params.get('threshold', 0.7)  # Default threshold of 0.7
        timeout = params.get('timeout', 20)  # Default timeout of 20 seconds

        if not image_path:
            return {"status": "error", "message": "Missing image_path parameter"}

        # Resolve the image path - try both absolute path and relative to reference_images directory
        if os.path.isabs(image_path) and os.path.exists(image_path):
            resolved_path = image_path
        else:
            # Try to resolve from reference_images directory
            try:
                from config import DIRECTORIES
                reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                resolved_path = os.path.join(reference_dir, os.path.basename(image_path))
                if not os.path.exists(resolved_path):
                    return {"status": "error", "message": f"Image file not found: {image_path}"}
            except (ImportError, KeyError):
                # Fallback path resolution
                ref_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'reference_images')
                resolved_path = os.path.join(ref_dir, os.path.basename(image_path))
                if not os.path.exists(resolved_path):
                    return {"status": "error", "message": f"Image file not found: {image_path}"}

        # Check if controller supports AirTest
        has_airtest = hasattr(self.controller, 'airtest_device') and self.controller.airtest_device

        self.logger.info(f"Executing double click on image: {resolved_path} with threshold: {threshold}")

        try:
            # Check if controller has a dedicated double_tap_image method
            if hasattr(self.controller, 'double_tap_image'):
                self.logger.info(f"Using controller.double_tap_image for: {resolved_path}")
                result = self.controller.double_tap_image(
                    image_path=resolved_path,
                    threshold=threshold,
                    timeout=timeout
                )

                # Handle different return types
                if isinstance(result, dict):
                    return result
                elif isinstance(result, tuple) and len(result) >= 2:
                    success, message = result[0], result[1]
                    position = result[2] if len(result) > 2 else None
                    return {
                        "status": "success" if success else "error",
                        "message": message,
                        "position": position
                    }
                elif result is True or result:
                    return {"status": "success", "message": f"Double clicked image: {image_path}"}
                else:
                    return {"status": "error", "message": f"Failed to double click image: {image_path}"}

            # Try using AirTest APIs directly
            if has_airtest:
                try:
                    self.logger.info(f"Using AirTest APIs directly for: {resolved_path}")
                    # Import AirTest functions
                    from airtest.core.api import double_click, Template, exists

                    # Create template with the specified threshold
                    template = Template(resolved_path, threshold=threshold)

                    # Check if image exists on screen
                    pos = exists(template)
                    if not pos:
                        return {
                            "status": "error",
                            "message": f"Image not found: {image_path} (timeout: {timeout}s, threshold: {threshold})"
                        }

                    # Double click at the position
                    double_click(pos)

                    return {
                        "status": "success",
                        "message": f"Double clicked image: {image_path}",
                        "position": pos,
                        "confidence": pos[0] if isinstance(pos, tuple) and len(pos) > 0 else None
                    }
                except ImportError:
                    self.logger.warning("AirTest API not available, using OpenCV fallback")
                except Exception as e:
                    self.logger.error(f"Error using AirTest API: {e}, trying OpenCV fallback")

            # OpenCV fallback when AirTest is not available
            self.logger.info(f"Using OpenCV fallback for: {resolved_path}")
            return self._opencv_double_click_image(resolved_path, threshold, timeout)

        except Exception as e:
            self.logger.error(f"Error executing double click image action: {e}")
            return {"status": "error", "message": f"Double click image action failed: {str(e)}"}

    def _opencv_double_click_image(self, image_path, threshold, timeout):
        """Fallback implementation using OpenCV for template matching and Appium for double click"""
        try:
            import cv2
            import numpy as np
            import time

            # Check if image exists
            if not os.path.exists(image_path):
                return {"status": "error", "message": f"Image not found: {image_path}"}

            # Take screenshot using device controller
            if not hasattr(self.controller, 'take_screenshot'):
                return {"status": "error", "message": "Device controller does not support screenshots"}

            start_time = time.time()
            while time.time() - start_time < timeout:
                # Take screenshot
                screenshot_path = self.controller.take_screenshot()
                if not screenshot_path or not os.path.exists(screenshot_path):
                    time.sleep(1)
                    continue

                # Load images
                screenshot = cv2.imread(screenshot_path)
                template = cv2.imread(image_path)

                if screenshot is None or template is None:
                    return {"status": "error", "message": "Failed to load images for template matching"}

                # Perform template matching
                result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
                _, max_val, _, max_loc = cv2.minMaxLoc(result)

                self.logger.info(f"Template matching result: {max_val} (threshold: {threshold})")

                if max_val >= threshold:
                    # Found the image, get center coordinates
                    h, w = template.shape[:2]
                    center_x = max_loc[0] + w // 2
                    center_y = max_loc[1] + h // 2

                    # Execute double tap at coordinates
                    if hasattr(self.controller, 'tap'):
                        # First tap
                        self.controller.tap(center_x, center_y)
                        # Short delay
                        time.sleep(0.1)
                        # Second tap
                        self.controller.tap(center_x, center_y)

                        return {
                            "status": "success",
                            "message": f"Double clicked image {os.path.basename(image_path)} at ({center_x}, {center_y})",
                            "position": (center_x, center_y),
                            "confidence": max_val
                        }
                    else:
                        return {"status": "error", "message": "Device controller does not support tap operations"}

                # Short delay before next attempt
                time.sleep(1)

            # Timeout reached
            return {"status": "error", "message": f"Image not found within timeout: {timeout}s"}

        except Exception as e:
            self.logger.error(f"OpenCV double click image failed: {str(e)}")
            return {"status": "error", "message": f"OpenCV double click operation failed: {str(e)}"}